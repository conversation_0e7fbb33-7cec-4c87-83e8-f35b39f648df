<?php

namespace pages\backoffice\api_v2\draw_management\line_items\docs;

use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\lendingwise\tblDrawRequestLineItems;
use models\standard\HTTP;
use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;

/**
 * API endpoint for retrieving documents related to draw request line items
 *
 * @package pages\backoffice\api_v2\draw_management\line_items\docs
 */
class docs extends DrawManagementApiBase
{
    /**
     * Handle GET requests for retrieving line item documents
     */
    public static function Get(): void
    {
        parent::Init();

        static::executeWithErrorHandling(function() {
            $lineItemId = $_GET['lineItemId'] ?? null;

            if (!$lineItemId) {
                static::sendErrorResponse('Missing required parameter: lineItemId');
                return;
            }

            // Validate line item exists
            $lineItemRecord = tblDrawRequestLineItems::Get(['id' => $lineItemId]);
            if (!$lineItemRecord) {
                static::sendErrorResponse('Line item not found');
                return;
            }

            // Get line item documents
            $lineItem = new BorrowerDrawLineItem($lineItemRecord);
            $documents = $lineItem->getDocuments();

            static::sendSuccessResponse([
                'documents' => $documents,
                'count' => count($documents)
            ], 'Documents retrieved successfully');

        }, 'Failed to retrieve documents');
    }
}
