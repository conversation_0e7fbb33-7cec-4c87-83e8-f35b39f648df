<?php

namespace pages\backoffice\api_v2\draw_management\line_items\upload_docs;

use models\composite\oFileDoc\saveFileDocument;
use models\constants\gl\glMimeTypes;
use models\cypher;
use models\FileStorage;
use models\lendingwise\tblDrawRequestLineItems;
use models\composite\oDrawManagement\BorrowerDrawLineItem;
use models\PageVariables;
use models\standard\Strings;
use models\UploadServer;
use models\Log;
use pages\backoffice\api_v2\draw_management\base\DrawManagementApiBase;

/**
 * API endpoint for uploading documents related to draw request line items
 *
 * @package pages\backoffice\api_v2\draw_management\line_items\upload_docs
 */
class upload_docs extends DrawManagementApiBase
{
    public static ?int $LMRId = null;
    public static ?int $lineItemId = null;
    public static ?BorrowerDrawLineItem $borrowerLineItem = null;
    public static ?string $docName = null;
    public static ?string $fileExtension = null;
    public static ?int $docId = null;
    public static ?string $uploaderType = null;

    public static ?int $userNumber = null;
    public static ?string $userName = null;
    public static ?string $userGroup = null;

    /**
     * Handle POST requests for document upload
     * @return void
     */
    public static function Post(): void
    {
        parent::Init();

        static::executeWithErrorHandling(function() {
            // Initialize variables from request
            static::initializeVariables();

            // Validate required parameters
            if (!static::$lineItemId || !static::$LMRId) {
                static::sendErrorResponse('Missing required parameters: lineItemId and LMRId');
                return;
            }

            // Get and validate line item
            if (!static::validateLineItem()) {
                return;
            }

            // Validate loan file exists
            static::validateLoanFile(static::$LMRId);

            // Validate file upload
            if (!static::validateFileUpload()) {
                return;
            }

            // Process the upload
            static::saveUploadedDocument();

            // Return success response
            static::sendSuccessResponse([
                'docId' => static::$docId,
                'fileName' => $_FILES['file']['name'],
                'uploaderType' => static::$uploaderType,
                'uploadedDate' => date('Y-m-d H:i:s'),
                'documentCount' => static::$borrowerLineItem->getDocumentCount()
            ], 'Document uploaded successfully');

        }, 'Document upload failed');
    }

    /**
     * Initialize variables from request data
     * @return void
     */
    private static function initializeVariables(): void
    {
        static::$lineItemId = $_POST['lineItemId'] ?? null;
        static::$LMRId = static::getLMRId($_POST['LMRId'] ?? null);

        static::$userNumber = PageVariables::$userNumber;
        static::$userName = PageVariables::$userName;
        static::$userGroup = PageVariables::$userGroup;

        static::$uploaderType = static::determineUploaderType();
    }

    /**
     * Validate line item exists and create BorrowerDrawLineItem instance
     * @return bool
     */
    private static function validateLineItem(): bool
    {
        $lineItemRecord = tblDrawRequestLineItems::Get(['id' => static::$lineItemId]);
        if (!$lineItemRecord) {
            static::sendErrorResponse('Line item not found');
            return false;
        }

        static::$borrowerLineItem = new BorrowerDrawLineItem($lineItemRecord);
        return true;
    }

    /**
     * Validate file upload
     * @return bool
     */
    private static function validateFileUpload(): bool
    {
        if (!isset($_FILES['file']) || $_FILES['file']['error'] !== UPLOAD_ERR_OK) {
            static::sendErrorResponse('File upload failed or no file provided');
            return false;
        }

        $file = $_FILES['file'];

        // File type validation
        if (!in_array($file['type'], glMimeTypes::$glMimeTypes)) {
            static::sendErrorResponse('Unsupported file type: ' . $file['type']);
            return false;
        }

        // File size validation
        if ($file['size'] > CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED) {
            $maxSizeMB = CONST_GLUPLOAD_MAX_BYTESFILESIZE_ALLOWED / (1024*1024);
            static::sendErrorResponse("File size too large. Maximum allowed: {$maxSizeMB}MB");
            return false;
        }

        if ($file['size'] == 0) {
            static::sendErrorResponse('Empty file not allowed');
            return false;
        }

        return true;
    }

    /**
     * Save uploaded document following the loan upload pattern
     * @return void
     */
    private static function saveUploadedDocument(): void
    {
        $infoArray = static::prepareInfoArray();
        $infoArray = static::processUploadedFile($infoArray);

        static::saveDocument($infoArray);
        static::uploadDocumentToServer($infoArray);
        static::handleDocumentAssociation();
    }

    /**
     * Prepare info array for document saving
     * @return array
     */
    private static function prepareInfoArray(): array
    {
        return [
            'userGroup' => static::$userGroup,
            'userNumber' => static::$userNumber,
            'userName' => static::$userName,
            'uploadedBy' => static::$userNumber,
            'uploadingUserType' => static::$userGroup,
            'saveTab' => 'DOC',
            'saveNotes' => 1,
            'LMRID' => static::$LMRId,
            'eLMRID' => cypher::myEncryption(static::$LMRId),
            'docCategory' => 'Draw Management Line Item',
            'recordDate' => date('Y-m-d H:i:s'),
        ];
    }

    /**
     * Process uploaded file and add file-specific data to info array
     * @param array $infoArray
     * @return array
     */
    private static function processUploadedFile(array $infoArray): array
    {
        $file = $_FILES['file'];
        $fileName = $file['name'];
        $fileTmpName = $file['tmp_name'];

        static::$fileExtension = pathinfo($fileName, PATHINFO_EXTENSION);
        static::$docName = Strings::removeDisAllowedChars($fileName);

        $infoArray['tmpFileContent'] = base64_encode(FileStorage::getFile($fileTmpName));
        $infoArray['fileExtension'] = static::$fileExtension;
        $infoArray['docName'] = pathinfo(static::$docName, PATHINFO_FILENAME);

        return $infoArray;
    }

    /**
     * Save document to tblLMRFileDocs
     * @param array $infoArray
     * @return void
     */
    private static function saveDocument(array $infoArray): void
    {
        Log::Insert('saveFileDocument::getReport - Start (Draw Line Item)');
        static::$docId = saveFileDocument::getReport($infoArray);
        Log::Insert('saveFileDocument::getReport - End (Draw Line Item)');

        if (!static::$docId) {
            static::sendErrorResponse('Document not uploaded - failed to save metadata');
        }
    }

    /**
     * Upload document to server
     * @param array $infoArray
     * @return void
     */
    private static function uploadDocumentToServer(array $infoArray): void
    {
        // Create unique filename for line item document
        $docNameWithoutExt = pathinfo(static::$docName, PATHINFO_FILENAME);
        $infoArray['docId'] = static::$docId;
        $infoArray['fileDocName'] = $docNameWithoutExt . '_' . static::$lineItemId . '_' . cypher::myEncryption(static::$docId) . '.' . static::$fileExtension;

        Log::Insert('UploadServer::upload - Start (Draw Line Item)');
        $result = UploadServer::upload($infoArray);
        Log::Insert('UploadServer::upload - End (Draw Line Item)');

        if ($result !== 'Success') {
            // If upload fails, deactivate the document record
            $docRecord = \models\lendingwise\tblLMRFileDocs::Get(['docID' => static::$docId]);
            if ($docRecord) {
                $docRecord->activeStatus = 0;
                $docRecord->Save();
            }
            static::sendErrorResponse('Document not uploaded - failed to upload to server');
        }
    }

    /**
     * Associate document with line item using BorrowerDrawLineItem
     * @return void
     */
    private static function handleDocumentAssociation(): void
    {
        $result = static::$borrowerLineItem->addDocument(
            static::$docId,
            static::$userNumber,
            static::$uploaderType
        );

        if (!$result) {
            static::sendErrorResponse('Failed to associate document with line item');
        }
    }

    /**
     * Determine uploader type based on user group and context
     * @return string
     */
    private static function determineUploaderType(): string
    {
        $userGroup = PageVariables::$userGroup;

        // If user is accessing from borrower portal or is a borrower type user
        if (in_array($userGroup, ['Borrower', 'Co-Borrower', 'BorrowerUser']) ||
            strpos($_SERVER['REQUEST_URI'], 'borrower') !== false) {
            return 'borrower';
        }

        return 'lender';
    }
}
