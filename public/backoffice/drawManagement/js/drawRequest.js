/**
 * Common Draw Request Functions
 * Shared functionality between borrower and lender draw request forms
 */
window.DrawRequestUtils = window.DrawRequestUtils || {};

DrawRequestUtils.calculateAmountFromPercent = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const cost = parseFloat($percentInput.data('cost')) || 0;
    const requestedAmount = (percent / 100) * cost;

    return Math.max(0, requestedAmount);
};

DrawRequestUtils.calculatePercentFromAmount = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;

    const percent = cost > 0 ? (amount / cost) * 100 : 0;
    return Math.max(0, Math.min(100, percent));
};

DrawRequestUtils.validatePercentInput = function($percentInput) {
    const percent = parseFloat($percentInput.val()) || 0;
    const completedPercent = parseFloat($percentInput.data('completed-percent')) || 0;
    const maxPercent = 100 - completedPercent;

    const $validationMsg = $percentInput.closest('td').find('.validation-message');

    if (percent < 0) {
        $validationMsg.text('Percentage cannot be negative').show();
        $percentInput.addClass('is-invalid');
        return false;
    } else if (percent > maxPercent) {
        $validationMsg.text(`Percentage cannot exceed ${maxPercent.toFixed(2)}% (100% - ${completedPercent.toFixed(2)}% completed)`).show();
        $percentInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $percentInput.removeClass('is-invalid');
        return true;
    }
};

DrawRequestUtils.validateAmountInput = function($amountInput) {
    const amount = parseFloat($amountInput.val()) || 0;
    const cost = parseFloat($amountInput.data('cost')) || 0;
    const completedAmount = parseFloat($amountInput.data('completed-amount')) || 0;
    const maxAmount = cost - completedAmount;

    const $validationMsg = $amountInput.closest('td').find('.validation-message');

    if (amount < 0) {
        $validationMsg.text('Amount cannot be negative').show();
        $amountInput.addClass('is-invalid');
        return false;
    } else if (amount > maxAmount) {
        $validationMsg.text(`Amount cannot exceed $${maxAmount.toFixed(2)} (Total Budget - Completed Renovations)`).show();
        $amountInput.addClass('is-invalid');
        return false;
    } else {
        $validationMsg.hide();
        $amountInput.removeClass('is-invalid');
        return true;
    }
};

DrawRequestUtils.getPercentageColor = function(percentage) {
    const p = Math.max(0, Math.min(100, percentage));

    if (p < 25) {
        return 'bg-danger';
    } else if (p < 50) {
        return 'bg-info';
    } else if (p < 75) {
        return 'bg-primary';
    } else {
        return 'bg-success';
    }
};

DrawRequestUtils.initializePercentageColors = function() {
    $('.percentage').each(function() {
        const percentage = parseInt($(this).text());
        $(this).addClass(DrawRequestUtils.getPercentageColor(percentage));
    });
};

DrawRequestUtils.setupInputHandlers = function(additionalValidationCallback) {
    $('.requested-percent').on('input', function() {
        const $percentInput = $(this);
        const lineItemId = $percentInput.data('line-item-id');
        const $amountInput = $(`.requested-amount[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validatePercentInput($percentInput)) {
            const calculatedAmount = DrawRequestUtils.calculateAmountFromPercent($percentInput);
            $amountInput.val(calculatedAmount.toFixed(2));
            DrawRequestUtils.validateAmountInput($amountInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    $('.requested-amount').on('input', function() {
        const $amountInput = $(this);
        const lineItemId = $amountInput.data('line-item-id');
        const $percentInput = $(`.requested-percent[data-line-item-id="${lineItemId}"]`);

        if (DrawRequestUtils.validateAmountInput($amountInput)) {
            const calculatedPercent = DrawRequestUtils.calculatePercentFromAmount($amountInput);
            $percentInput.val(calculatedPercent.toFixed(2));
            DrawRequestUtils.validatePercentInput($percentInput);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });

    $('.requested-percent, .requested-amount').on('blur', function() {
        const $input = $(this);
        if ($input.hasClass('requested-percent')) {
            DrawRequestUtils.validatePercentInput($input);
        } else {
            DrawRequestUtils.validateAmountInput($input);
        }

        if (additionalValidationCallback && typeof additionalValidationCallback === 'function') {
            additionalValidationCallback();
        }
    });
};

DrawRequestUtils.validateAllInputs = function() {
    let isValid = true;

    $('.requested-percent').each(function() {
        if (!DrawRequestUtils.validatePercentInput($(this))) {
            isValid = false;
        }
    });

    $('.requested-amount').each(function() {
        if (!DrawRequestUtils.validateAmountInput($(this))) {
            isValid = false;
        }
    });

    return isValid;
};

DrawRequestUtils.hasNonZeroValues = function() {
    let hasNonZeroValue = false;

    $('.requested-percent, .requested-amount').each(function() {
        const value = parseFloat($(this).val()) || 0;
        if (value > 0) {
            hasNonZeroValue = true;
            return false;
        }
    });

    return hasNonZeroValue;
};

DrawRequestUtils.markFunded = function() {
    $('span.percentage').each(function() {
        const $percentageElement = $(this);
        const percentage = parseInt($percentageElement.text());

        if (percentage === 100) {
            const $row = $percentageElement.closest('tr.line-item');
            $row.addClass('funded');
            $row.find('.requested-percent').prop('disabled', true);
            $row.find('.requested-amount').prop('disabled', true);
            $row.css('background', 'linear-gradient(to right, #5ac272ff 0%, #fff 100%)');
        }
    });
};

DrawRequestUtils.hasValidationErrors = function() {
    return $('.is-invalid').length > 0;
};

$(document).ready(function() {
    DrawRequestUtils.initializePercentageColors();
    DrawRequestUtils.markFunded();

    // Initialize document view functionality
    DrawRequestUtils.initDocumentHandlers();
});

/**
 * Document Management Functions
 */
DrawRequestUtils.initDocumentHandlers = function() {
    // Event handler for view-docs-btn in scope of work table
    $(document).on('click', '.view-docs-btn', function(e) {
        e.preventDefault();
        const lineItemId = $(this).data('line-item-id');

        if (!lineItemId) {
            toastrNotification('Line item ID not found', 'error');
            return;
        }

        DrawRequestUtils.viewLineItemDocuments(lineItemId);
    });
};

DrawRequestUtils.viewLineItemDocuments = function(lineItemId) {
    $.ajax({
        url: '/backoffice/api_v2/draw_management/line_items/docs',
        type: 'GET',
        data: { lineItemId: lineItemId },
        dataType: 'json',
        success: function(response) {
            if (response.success) {
                DrawRequestUtils.showDocumentsModal(lineItemId, response.data.documents);
            } else {
                toastrNotification('Failed to load documents: ' + (response.message || 'Unknown error'), 'error');
            }
        },
        error: function(xhr, status, error) {
            toastrNotification('Error loading documents: ' + error, 'error');
        }
    });
};

DrawRequestUtils.showDocumentsModal = function(lineItemId, documents) {
    let modalContent = `
        <div class="modal fade" id="lineItemDocsModal" tabindex="-1" role="dialog">
            <div class="modal-dialog modal-lg" role="document">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">Line Item Documents</h5>
                        <button type="button" class="close" data-dismiss="modal">
                            <span>&times;</span>
                        </button>
                    </div>
                    <div class="modal-body">`;

    if (documents.length === 0) {
        modalContent += '<p class="text-center text-muted">No documents found for this line item.</p>';
    } else {
        modalContent += `
            <div class="table-responsive">
                <table class="table table-sm">
                    <thead>
                        <tr>
                            <th>Document Name</th>
                            <th>Uploaded By</th>
                            <th>Upload Date</th>
                            <th>Type</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>`;

        documents.forEach(function(doc) {
            const uploadDate = new Date(doc.uploadedDate).toLocaleDateString();
            const docName = doc.displayDocName || doc.docName || 'Unnamed Document';
            modalContent += `
                <tr>
                    <td>${docName}</td>
                    <td>${doc.uploaderType}</td>
                    <td>${uploadDate}</td>
                    <td>${doc.fileExtension || 'Unknown'}</td>
                    <td>
                        <a href="#" class="btn btn-sm btn-outline-primary view-doc-btn"
                           data-doc-id="${doc.docID}" title="View Document">
                            <i class="fas fa-eye"></i>
                        </a>
                    </td>
                </tr>`;
        });

        modalContent += `
                    </tbody>
                </table>
            </div>`;
    }

    modalContent += `
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-dismiss="modal">Close</button>
                    </div>
                </div>
            </div>
        </div>`;

    // Remove existing modal if any
    $('#lineItemDocsModal').remove();

    // Add modal to page and show
    $('body').append(modalContent);
    $('#lineItemDocsModal').modal('show');

    // Handle document view clicks
    $('#lineItemDocsModal').on('click', '.view-doc-btn', function(e) {
        e.preventDefault();
        const docId = $(this).data('doc-id');
        // Open document in new tab/window
        const viewUrl = `/public/pops/viewDocs.php?docId=${docId}`;
        window.open(viewUrl, '_blank');
    });

    // Clean up modal when closed
    $('#lineItemDocsModal').on('hidden.bs.modal', function() {
        $(this).remove();
    });
};
